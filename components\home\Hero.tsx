'use client';

import Image from 'next/image';
import Link from 'next/link';
import TypewriterEffect from './TypewriterEffect';

export default function Hero() {
  return (
    <section className="relative h-screen w-full overflow-hidden">
      {/* Background Video */}
      <video
        src="/video.mp4"
        autoPlay
        muted
        loop
        playsInline
        className="absolute inset-0 h-full w-full object-cover -z-20"
      />

      {/* Overlay & Logo */}
      <div className="absolute inset-0 bg-black/80 -z-10" />

<header className="absolute top-[75.4px] w-full z-50">
  <div className="max-w-[1440px] mx-auto px-4 md:px-8 lg:px-[160px] xl:px-[291px]">
    <div className="flex items-center gap-2">
      <img
        src="/logo-icon.png"
        alt="Logo Icon"
        className="w-[50.17px] h-[47.19px] shrink-0"
      />
      <div className="leading-none">
<h1 className="text-white font-bold text-[24px] leading-[28px] w-[160.824px] h-[36.746px] shrink-0 aspect-[160.82/36.75] mb-0">
  Prolytech
</h1>
<p className="text-[#05A0E2] text-[5.5px] font-bold tracking-[0.16px] max-w-[120px] leading-tight mt-[-4px]">
          DEFINING THE CURVE OF WHAT'S NEXT
        </p>
      </div>
    </div>
  </div>
</header>


      <div className="relative z-10 h-full w-full">
        <div className="flex flex-col items-start justify-end h-full text-left px-4 md:pl-[190px] md:pr-4 pb-12 md:pb-16">
          <h1 className="text-white text-2xl sm:text-3xl md:text-4xl font-semibold leading-snug">
            <span className="block">Powering the Next Wave</span>
            <span className="inline-flex items-center">
              of&nbsp;
              <TypewriterEffect 
                phrases={[
                  'Digital Innovation',
                  'Intelligent Automation',
                  'Scalable Growth',
                  'Cloud Scalability'
                ]}
                className="text-cyan-400"
                typingSpeed={50}
                deletingSpeed={25}
                pauseDuration={2500}
              />
            </span>
          </h1>

          <p className="text-gray-200 text-sm md:text-base mt-4 leading-relaxed max-w-md md:max-w-none">
            <span className="block md:hidden">We architect and deliver high-performance</span>
            <span className="block md:hidden">platforms—social, transactional, and</span>
            <span className="block md:hidden">intelligent—at startup speed and enterprise scale.</span>

            <span className="hidden md:block">We architect and deliver high-performance platforms—social, transactional,</span>
            <span className="hidden md:block">and intelligent—at startup speed and enterprise scale.</span>
          </p>

          <Link
            href="/contact"
            className="mt-6 inline-block px-6 py-2 text-white font-medium text-sm md:text-base rounded-full shadow-md transition-all duration-300
              bg-[linear-gradient(to_bottom,#04E6FC,#0664CC)] hover:bg-[linear-gradient(to_bottom,#04c9de,#0559b4)]"
          >
            Schedule a Consultation
          </Link>
        </div>
      </div>
    </section>
  );
}
